const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
const { Document, Packer, Paragraph, Table, TableRow, TableCell, WidthType, AlignmentType, HeadingLevel, TextRun, BorderStyle } = require('docx');

// 数据库连接配置
const dbConfig = {
  host: '*************',
  port: 3306,
  user: 'root',
  password: 'Nod%D*h2y2*oUZuz',
  charset: 'utf8mb4'
};

// 获取所有数据库
async function getAllDatabases(connection) {
  try {
    const [rows] = await connection.query('SHOW DATABASES');
    const systemDbs = ['information_schema', 'performance_schema', 'mysql', 'sys'];
    const databases = rows
      .map(row => row.Database)
      .filter(db => !systemDbs.includes(db) && !db.toLowerCase().includes('mock'));
    
    return databases;
  } catch (error) {
    console.error('获取数据库列表失败:', error);
    return [];
  }
}

// 获取数据库中的所有表
async function getTablesInDatabase(connection, database) {
  try {
    await connection.query(`USE \`${database}\``);
    const [rows] = await connection.query('SHOW TABLES');
    const tableKey = `Tables_in_${database}`;
    return rows.map(row => row[tableKey]);
  } catch (error) {
    console.error(`获取数据库 ${database} 的表列表失败:`, error);
    return [];
  }
}

// 获取表结构信息
async function getTableStructure(connection, database, tableName) {
  try {
    // 获取表的列信息
    const [columns] = await connection.execute(`
      SELECT
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT,
        COLUMN_KEY,
        EXTRA,
        CHARACTER_MAXIMUM_LENGTH,
        NUMERIC_PRECISION,
        NUMERIC_SCALE
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      ORDER BY ORDINAL_POSITION
    `, [database, tableName]);

    // 获取表注释
    const [tableInfo] = await connection.execute(`
      SELECT TABLE_COMMENT
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
    `, [database, tableName]);

    return {
      tableName,
      tableComment: tableInfo[0]?.TABLE_COMMENT || '',
      columns
    };
  } catch (error) {
    console.error(`获取表 ${database}.${tableName} 结构失败:`, error);
    return null;
  }
}

// 创建表格行
function createTableRow(cells, isHeader = false) {
  return new TableRow({
    children: cells.map(cell => new TableCell({
      children: [new Paragraph({
        children: [new TextRun({
          text: cell,
          bold: isHeader,
          size: isHeader ? 24 : 20
        })],
        alignment: AlignmentType.CENTER
      })],
      width: {
        size: 100 / cells.length,
        type: WidthType.PERCENTAGE,
      },
    }))
  });
}

// 创建表结构表格
function createTableStructureTable(tableStructure) {
  const { tableName, tableComment, columns } = tableStructure;

  const elements = [];

  // 表名标题
  elements.push(new Paragraph({
    children: [new TextRun({
      text: `${tableName}`,
      bold: true,
      size: 24
    })],
    heading: HeadingLevel.HEADING_3,
    spacing: { before: 300, after: 100 }
  }));

  // 表注释
  if (tableComment) {
    elements.push(new Paragraph({
      children: [new TextRun({
        text: `表注释: ${tableComment}`,
        size: 20,
        italics: true
      })],
      spacing: { after: 150 }
    }));
  }

  // 字段信息表格
  const tableRows = [
    createTableRow(['字段名', '数据类型', '是否为空', '默认值', '字段注释'], true)
  ];

  columns.forEach(column => {
    const dataType = column.CHARACTER_MAXIMUM_LENGTH
      ? `${column.DATA_TYPE}(${column.CHARACTER_MAXIMUM_LENGTH})`
      : column.NUMERIC_PRECISION
        ? `${column.DATA_TYPE}(${column.NUMERIC_PRECISION}${column.NUMERIC_SCALE ? ',' + column.NUMERIC_SCALE : ''})`
        : column.DATA_TYPE;

    const nullable = column.IS_NULLABLE === 'YES' ? '是' : '否';
    const defaultValue = column.COLUMN_DEFAULT || '';
    const comment = column.COLUMN_COMMENT || '无注释';

    tableRows.push(createTableRow([
      column.COLUMN_NAME,
      dataType,
      nullable,
      defaultValue,
      comment
    ]));
  });

  elements.push(new Table({
    rows: tableRows,
    width: {
      size: 100,
      type: WidthType.PERCENTAGE,
    },
    borders: {
      top: { style: BorderStyle.SINGLE, size: 1 },
      bottom: { style: BorderStyle.SINGLE, size: 1 },
      left: { style: BorderStyle.SINGLE, size: 1 },
      right: { style: BorderStyle.SINGLE, size: 1 },
      insideHorizontal: { style: BorderStyle.SINGLE, size: 1 },
      insideVertical: { style: BorderStyle.SINGLE, size: 1 },
    },
  }));

  // 添加表格后的间距
  elements.push(new Paragraph({
    children: [new TextRun({ text: '', size: 20 })],
    spacing: { before: 200, after: 200 }
  }));

  return elements;
}

// 主函数
async function generateWordDocument() {
  let connection;
  
  try {
    console.log('开始连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功!');
    
    const databases = await getAllDatabases(connection);
    console.log(`发现数据库: ${databases.join(', ')}`);
    
    // 创建Word文档
    const docElements = [];
    
    // 文档标题
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: 'MySQL数据库结构分析报告',
        bold: true,
        size: 36
      })],
      heading: HeadingLevel.TITLE,
      alignment: AlignmentType.CENTER,
      spacing: { after: 400 }
    }));
    
    // 基本信息
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: `分析时间: ${new Date().toLocaleString()}`,
        size: 24
      })],
      spacing: { after: 200 }
    }));
    
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: `数据库服务器: ${dbConfig.host}:${dbConfig.port}`,
        size: 24
      })],
      spacing: { after: 200 }
    }));
    
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: `分析的数据库数量: ${databases.length}`,
        size: 24
      })],
      spacing: { after: 400 }
    }));
    
    // 数据库概览
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: '数据库概览',
        bold: true,
        size: 32
      })],
      heading: HeadingLevel.HEADING_1,
      spacing: { before: 400, after: 200 }
    }));

    for (let i = 0; i < databases.length; i++) {
      const database = databases[i];
      const tables = await getTablesInDatabase(connection, database);

      docElements.push(new Paragraph({
        children: [new TextRun({
          text: `${i + 1}. ${database} (${tables.length}个表)`,
          size: 24
        })],
        spacing: { after: 100 }
      }));
    }

    // 详细表结构 - 每个数据库作为一个章节
    for (const database of databases) {
      console.log(`\n处理数据库: ${database}`);

      // 数据库章节标题
      docElements.push(new Paragraph({
        children: [new TextRun({
          text: `第${databases.indexOf(database) + 1}章 ${database} 数据库`,
          bold: true,
          size: 32
        })],
        heading: HeadingLevel.HEADING_1,
        spacing: { before: 600, after: 300 }
      }));

      const tables = await getTablesInDatabase(connection, database);
      console.log(`发现 ${tables.length} 个表`);

      // 数据库基本信息
      docElements.push(new Paragraph({
        children: [new TextRun({
          text: `数据库名称: ${database}`,
          size: 24
        })],
        spacing: { after: 100 }
      }));

      docElements.push(new Paragraph({
        children: [new TextRun({
          text: `表数量: ${tables.length}`,
          size: 24
        })],
        spacing: { after: 300 }
      }));

      // 分析每个表
      for (const tableName of tables) {
        console.log(`  处理表: ${tableName}`);
        const tableStructure = await getTableStructure(connection, database, tableName);

        if (tableStructure) {
          const tableElements = createTableStructureTable(tableStructure);
          docElements.push(...tableElements);
        }
      }
    }
    
    // 创建文档
    const doc = new Document({
      sections: [{
        properties: {},
        children: docElements
      }]
    });
    
    // 保存文档
    const outputPath = path.join('./database-analysis', 'database-structure-analysis.docx');
    const buffer = await Packer.toBuffer(doc);
    await fs.writeFile(outputPath, buffer);
    
    console.log(`\nWord文档已生成: ${outputPath}`);
    console.log('文档生成完成!');
    
  } catch (error) {
    console.error('生成Word文档过程中发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 运行生成
if (require.main === module) {
  generateWordDocument().catch(console.error);
}

module.exports = { generateWordDocument };
